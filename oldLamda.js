const { Pool } = require('pg');

const pool = new Pool({
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_DATABASE,
    ssl: {
        rejectUnauthorized: false
    }
});

// Helper to add CORS headers to every response
function withCors(response) {
    return {
        ...response,
        headers: {
            'Access-Control-Allow-Origin': '*',
            // add these if you want to support credentials or extra headers:
            // 'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,OPTIONS,DELETE',
            ...response.headers,
        }
    };
}

exports.handler = async (event) => {
    console.log("Event received:", JSON.stringify(event));

    try {
        if (event.httpMethod === 'OPTIONS') {
            return withCors({
                statusCode: 200,
                body: '',
              });
        }

        // Check for API key
        const apiKey = event.requestContext?.identity?.apiKey;
        if (!apiKey || apiKey !== process.env.API_KEY) {
            return withCors({
                statusCode: 401,
                body: JSON.stringify({
                    error: 'Unauthorized: Invalid API key',
                    providedApiKey: apiKey || null
                })
            });
        }

        switch (event.httpMethod) {
            case 'GET':
                return withCors(await handleGet(event));
            case 'POST':
                return withCors(await handlePost(event));
            case 'PUT':
                return withCors(await handlePut(event));
            case 'DELETE':
                return withCors(await handleDelete(event));
            default:
                return withCors({
                    statusCode: 405,
                    body: JSON.stringify({
                        error: 'Method not allowed. This is what was received:',
                        receivedMethod: event.httpMethod,
                        receivedPath: event.path
                    })
                });
        }
    } catch (error) {
        console.error('Error stack:', error.stack);
        console.error('Error message:', error.message);
        return withCors({
            statusCode: 500,
            body: JSON.stringify({ error: 'Internal server error', message: error.message })
        });
    }
};

async function handleGet(event) {
    let { pilot, drone_name, datetime, page, archives, property_name } = event.queryStringParameters || {};

    // Archive request detection
    if (archives === 'true') {
        return await handleGetArchived(event);
    }

    // Original logic unchanged - maintains backward compatibility
    console.log("Received pilot:", pilot);
    console.log("Received drone_name:", drone_name);
    console.log("Received datetime:", datetime);
    console.log("Received page:", page);

    if (!pilot || !drone_name || !datetime) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required parameters: pilot, drone_name, and datetime are required'
            })
        };
    }

    const PAGE_SIZE = 3;
    let getQuery;
    let params;

    if (pilot === "Master" && drone_name === "Master") {
        const pageNumber = parseInt(page, 10) || 1;
        const offset = (pageNumber - 1) * PAGE_SIZE;

        getQuery = `
            SELECT * FROM surveys
            ORDER BY scheduled_time ASC, id ASC
            LIMIT $1 OFFSET $2
        `;
        params = [PAGE_SIZE, offset];
    } else {
        // Subtract one day from the datetime
        const oneDayBefore = new Date(datetime);
        oneDayBefore.setDate(oneDayBefore.getDate() - 1);
        const adjustedDatetime = oneDayBefore.toISOString();

        getQuery = `
            SELECT * FROM surveys
            WHERE pilot = $1
            AND drone_name = $2
            AND scheduled_time >= $3
            ORDER BY scheduled_time ASC, id ASC
            LIMIT 5
        `;
        params = [pilot, drone_name, adjustedDatetime];
    }

    const result = await pool.query(getQuery, params);

    return {
        statusCode: 200,
        body: JSON.stringify({ data: result.rows })
    };
}

async function handlePost(event) {
    const body = JSON.parse(event.body || '{}');
    const {
        pilot,
        drone_name,
        gate_code,
        scheduled_time,
        coordinates,
        notes,
        driving_instructions,
        image_base64,
        google_sheets_id,
        property_name,
        pdf_base64
    } = body;

    if (!pilot || !drone_name || !gate_code || !scheduled_time || !coordinates) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required fields: pilot, drone_name, gate_code, scheduled_time, and coordinates are required'
            })
        };
    }

    const insertQuery = `
        INSERT INTO surveys (
            pilot, drone_name, gate_code, scheduled_time,
            coordinates, notes, driving_instructions,
            image, google_sheets_id, property_name, pdf
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
    `;

    const result = await pool.query(insertQuery, [
        pilot,
        drone_name,
        gate_code,
        scheduled_time,
        coordinates,
        notes || null,
        driving_instructions || null,
        image_base64 ? Buffer.from(image_base64, 'base64') : null,
        google_sheets_id || null,
        property_name || null,
        pdf_base64 ? Buffer.from(pdf_base64, 'base64') : null
    ]);

    return {
        statusCode: 201,
        body: JSON.stringify({
            message: 'Survey added successfully',
            survey: result.rows[0]
        })
    };
}

async function handlePut(event) {
    const body = JSON.parse(event.body || '{}');

    const { id } = body;

    if (!id) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required field to identify survey: id is required'
            })
        };
    }

    const updatableFields = [
        'pilot',
        'drone_name',
        'gate_code',
        'scheduled_time',
        'coordinates',
        'notes',
        'driving_instructions',
        'image_base64',
        'google_sheets_id',
        'property_name',
        'pdf_base64'
    ];

    const setClauses = [];
    const values = [];
    let paramIndex = 1;

    for (const field of updatableFields) {
        if (body[field] !== undefined) {
            if (field === 'image_base64') {
                values.push(Buffer.from(body[field], 'base64'));
                setClauses.push(`image = $${paramIndex}`);
            } else if (field === 'pdf_base64') {
                values.push(Buffer.from(body[field], 'base64'));
                setClauses.push(`pdf = $${paramIndex}`);
            } else {
                values.push(body[field]);
                setClauses.push(`${field} = $${paramIndex}`);
            }
            paramIndex++;
        }
    }

    if (setClauses.length === 0) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'No fields provided to update'
            })
        };
    }

    values.push(id);  // For WHERE clause

    const updateQuery = `
        UPDATE surveys
        SET ${setClauses.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
    `;

    try {
        const result = await pool.query(updateQuery, values);

        if (result.rowCount === 0) {
            return {
                statusCode: 404,
                body: JSON.stringify({ error: 'No matching survey found to update' })
            };
        }

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Survey updated successfully',
                survey: result.rows[0]
            })
        };
    } catch (error) {
        console.error('Update error:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                error: 'Internal server error during update',
                message: error.message
            })
        };
    }
}

async function handleDelete(event) {
    const { pilot, drone_name, datetime, id } = event.queryStringParameters || {};

    // Check if we have either ID (new way) or the legacy parameters (old way)
    if (!id && (!pilot || !drone_name || !datetime)) {
        return {
            statusCode: 400,
            body: JSON.stringify({
                error: 'Missing required parameters. Provide either: id OR (pilot, drone_name, and datetime)'
            })
        };
    }

    // Start a transaction to ensure both operations succeed or fail together
    const client = await pool.connect();
    
    try {
        await client.query('BEGIN');

        // STEP 1: GET the survey to delete (works for both ID and legacy approaches)
        let selectQuery;
        let selectParams;

        if (id) {
            // New ID-based approach
            console.log("Using ID-based deletion for survey ID:", id);
            selectQuery = `SELECT * FROM surveys WHERE id = $1`;
            selectParams = [parseInt(id, 10)];
        } else {
            // Legacy approach for backward compatibility
            console.log("Using legacy deletion for pilot:", pilot, "drone:", drone_name, "datetime:", datetime);
            selectQuery = `
                SELECT * FROM surveys
                WHERE pilot = $1
                AND drone_name = $2
                AND scheduled_time = $3
            `;
            selectParams = [pilot, drone_name, datetime];
        }

        const selectResult = await client.query(selectQuery, selectParams);

        // STEP 2: ERROR CHECK - ensure survey exists
        if (selectResult.rowCount === 0) {
            await client.query('ROLLBACK');
            return {
                statusCode: 404,
                body: JSON.stringify({ 
                    error: 'No matching survey found to delete',
                    searchedBy: id ? `id: ${id}` : `pilot: ${pilot}, drone: ${drone_name}, datetime: ${datetime}`
                })
            };
        }

        const surveysToArchive = selectResult.rows;
        console.log(`Found ${surveysToArchive.length} surveys to archive and delete`);

        // STEP 3: ARCHIVE all matching surveys
        const archiveQuery = `
            INSERT INTO "survey-archives" (
                original_id, pilot, drone_name, gate_code, scheduled_time,
                coordinates, notes, driving_instructions, image, 
                google_sheets_id, property_name, pdf, archived_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW())
            RETURNING *
        `;

        const archivedSurveys = [];
        
        // Archive each matching survey
        for (const survey of surveysToArchive) {
            const archiveResult = await client.query(archiveQuery, [
                survey.id,
                survey.pilot,
                survey.drone_name,
                survey.gate_code,
                survey.scheduled_time,
                survey.coordinates,
                survey.notes,
                survey.driving_instructions,
                survey.image,
                survey.google_sheets_id,
                survey.property_name,
                survey.pdf
            ]);
            
            archivedSurveys.push(archiveResult.rows[0]);
        }

        // STEP 4: DELETE the original surveys (use the same criteria as the SELECT)
        let deleteQuery;
        let deleteParams;

        if (id) {
            deleteQuery = `DELETE FROM surveys WHERE id = $1 RETURNING *`;
            deleteParams = [parseInt(id, 10)];
        } else {
            deleteQuery = `
                DELETE FROM surveys
                WHERE pilot = $1
                AND drone_name = $2
                AND scheduled_time = $3
                RETURNING *
            `;
            deleteParams = [pilot, drone_name, datetime];
        }

        const deleteResult = await client.query(deleteQuery, deleteParams);

        // Commit the transaction - both archive and delete succeeded
        await client.query('COMMIT');

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: `${deleteResult.rowCount} survey(s) deleted and archived successfully`,
                deletionMethod: id ? 'ID-based' : 'Legacy (pilot/drone/datetime)',
                count: deleteResult.rowCount,
                deleted: deleteResult.rows,
                archived: archivedSurveys
            })
        };

    } catch (error) {
        // Rollback the transaction on error
        await client.query('ROLLBACK');
        console.error('Delete and archive error:', error);
        throw error; // Re-throw to be caught by the outer try-catch
    } finally {
        // Release the client back to the pool
        client.release();
    }
}

async function handleGetArchived(event) {
    const { page, property_name } = event.queryStringParameters || {};
    
    console.log("Archived surveys request - page:", page, "property_name:", property_name);

    const PAGE_SIZE = 3;
    const pageNumber = parseInt(page, 10) || 1;
    const offset = (pageNumber - 1) * PAGE_SIZE;

    let getQuery;
    let params;
    let countQuery;
    let countParams;

    if (property_name) {
        // Search by property name with pagination
        getQuery = `
            SELECT * FROM "survey-archives"
            WHERE property_name ILIKE $1
            ORDER BY archived_at DESC, original_id ASC
            LIMIT $2 OFFSET $3
        `;
        params = [`%${property_name}%`, PAGE_SIZE, offset];
        
        countQuery = `
            SELECT COUNT(*) as total FROM "survey-archives"
            WHERE property_name ILIKE $1
        `;
        countParams = [`%${property_name}%`];
    } else {
        // Get all archived surveys with pagination
        getQuery = `
            SELECT * FROM "survey-archives"
            ORDER BY archived_at DESC, original_id ASC
            LIMIT $1 OFFSET $2
        `;
        params = [PAGE_SIZE, offset];
        
        countQuery = `SELECT COUNT(*) as total FROM "survey-archives"`;
        countParams = [];
    }

    try {
        // Get the paginated results
        const result = await pool.query(getQuery, params);
        
        // Get the total count for pagination metadata
        const countResult = await pool.query(countQuery, countParams);
        const totalRecords = parseInt(countResult.rows[0].total, 10);
        const totalPages = Math.ceil(totalRecords / PAGE_SIZE);

        return {
            statusCode: 200,
            body: JSON.stringify({
                data: result.rows,
                pagination: {
                    currentPage: pageNumber,
                    totalPages: totalPages,
                    totalRecords: totalRecords,
                    pageSize: PAGE_SIZE,
                    hasNextPage: pageNumber < totalPages,
                    hasPreviousPage: pageNumber > 1
                },
                filters: {
                    property_name: property_name || null
                }
            })
        };
    } catch (error) {
        console.error('Get archived surveys error:', error);
        throw error;
    }
}
