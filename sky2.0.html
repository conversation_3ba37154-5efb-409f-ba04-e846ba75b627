<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>SkySenderos</title>
  <style>
    body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    }

    .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    }

    h1 {
    text-align: center;
    color: #585c2c;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    font-weight: 700;
    }

    nav {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    }

    nav button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(45deg, #585c2c, #6b7030);
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(88, 92, 44, 0.3);
    }

    nav button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 92, 44, 0.4);
    background: linear-gradient(45deg, #6b7030, #787c34);
    }

    nav button:active {
    transform: translateY(0);
    }

    .page {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    }

    .page.active {
    display: block;
    animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
    }

    h2 {
    color: #585c2c;
    font-size: 1.8rem;
    margin-bottom: 25px;
    border-bottom: 3px solid #585c2c;
    padding-bottom: 10px;
    }

    form {
    display: grid;
    grid-template-columns: 180px 1fr;
    gap: 20px;
    align-items: center;
    max-width: 800px;
    }

    form label {
    justify-self: end;
    font-weight: 600;
    color: #585c2c;
    }

    input, textarea, select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
    }

    input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #585c2c;
    box-shadow: 0 0 0 3px rgba(88, 92, 44, 0.1);
    }

    textarea {
    resize: vertical;
    min-height: 100px;
    }

    button[type="submit"] {
    grid-column: span 2;
    padding: 15px 30px;
    margin-top: 20px;
    background: linear-gradient(45deg, #585c2c, #6b7030);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(88, 92, 44, 0.3);
    }

    button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(88, 92, 44, 0.4);
    background: linear-gradient(45deg, #6b7030, #787c34);
    }

    pre {
    margin-top: 30px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    }

    #view-surveys table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    #view-surveys th, #view-surveys td {
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    text-align: left;
    }

    #view-surveys th {
    background: linear-gradient(135deg, #585c2c, #6b7030);
    color: white;
    font-weight: 600;
    }

    #view-surveys tr:nth-child(even) {
    background-color: #f8f9fa;
    }

    #view-surveys tr:hover {
    background-color: #e8f5e8;
    transition: background-color 0.3s ease;
    }

    img {
    max-width: 100px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }

    #surveys-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-top: 20px;
    }

    .survey-card {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border: 2px solid #e0e0e0;
    padding: 25px;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    }

    .survey-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
    border-color: #585c2c;
    }

    .survey-info {
    flex: 1 1 70%;
    line-height: 1.6;
    }

    .survey-info p {
    margin: 8px 0;
    font-size: 14px;
    }

    .survey-info strong {
    color: #585c2c;
    font-weight: 600;
    }

    .survey-info button {
    margin-top: 15px;
    margin-right: 10px;
    padding: 8px 16px;
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    }

    .survey-info button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    background: linear-gradient(45deg, #c82333, #bd2130);
    }

    .survey-info button.edit-btn {
    background: linear-gradient(45deg, #007bff, #0056b3);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    }

    .survey-info button.edit-btn:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
    }

    .survey-info button.duplicate-btn {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    .survey-info button.duplicate-btn:hover {
    background: linear-gradient(45deg, #1e7e34, #155724);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    .survey-attachments {
    flex: 0 0 30%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
    }

    .survey-image img {
    max-width: 300px;
    max-height: 200px;
    border-radius: 12px;
    object-fit: contain;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
    }

    .survey-image img:hover {
    transform: scale(1.05);
    }

    .pdf-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    width: 100%;
    }

    .pdf-viewer {
    width: 300px;
    height: 200px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    background: #f8f9fa;
    cursor: pointer;
    position: relative; /* Add positioning for overlay */
    }

    .pdf-viewer:hover {
    transform: scale(1.02);
    border-color: #585c2c;
    box-shadow: 0 12px 30px rgba(0,0,0,0.2);
    }

    .pdf-viewer iframe {
    width: 100%;
    height: 100%;
    border: none;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    }

    .pdf-click-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 255, 0.2); /* Blue tint to make it very visible */
    cursor: pointer;
    z-index: 100; /* Much higher z-index */
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .pdf-click-overlay:hover {
    background: rgba(88, 92, 44, 0.4);
    }

    .pdf-controls {
    display: none; /* Hide the controls section */
    }

    .pdf-link {
    display: none; /* Hide all PDF links */
    }

    .pdf-icon {
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
    color: #dc3545;
    }

    .pdf-error {
    width: 300px;
    height: 200px;
    border: 2px dashed #ccc;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #666;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    }

    .pdf-error:hover {
    border-color: #585c2c;
    background: #f0f0f0;
    }

    .survey-attachments {
    flex: 0 0 30%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
    width: 300px; /* Fixed width for consistent alignment */
    }

    .survey-image {
    width: 100%;
    display: flex;
    justify-content: center;
    }

    .survey-image img {
    max-width: 300px;
    max-height: 200px;
    border-radius: 12px;
    object-fit: contain;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
    }

    .survey-image img:hover {
    transform: scale(1.05);
    }

    /* PDF Modal for fullscreen viewing */
    .pdf-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    }

    .pdf-modal-content {
    position: relative;
    margin: 2% auto;
    width: 95%;
    height: 95%;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    }

    .pdf-modal-header {
    background: #585c2c;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    }

    .pdf-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    }

    .pdf-modal-close:hover {
    opacity: 0.7;
    }

    .pdf-modal iframe {
    width: 100%;
    height: calc(100% - 60px);
    border: none;
    }

    .file-input-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    }

    .file-input-label {
    font-size: 12px;
    color: #666;
    font-style: italic;
    }

    .pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 30px 0;
    }

    .pagination-controls button {
    padding: 10px 20px;
    border: 2px solid #585c2c;
    background: white;
    color: #585c2c;
    cursor: pointer;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(88, 92, 44, 0.2);
    }

    .pagination-controls button:hover:not(:disabled) {
    background: linear-gradient(45deg, #585c2c, #6b7030);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(88, 92, 44, 0.3);
    }

    .pagination-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    }

    .pagination-info {
    font-size: 16px;
    color: #585c2c;
    font-weight: 600;
    padding: 10px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .loading {
    text-align: center;
    color: #585c2c;
    margin: 40px 0;
    font-size: 18px;
    font-weight: 600;
    }

    .loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #585c2c;
    border-top: 3px solid transparent;
    border-radius: 50%;
    margin-left: 10px;
    animation: spin 1s linear infinite;
    }

    @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
    }

    .no-surveys {
    text-align: center;
    color: #666;
    margin: 60px 0;
    font-style: italic;
    font-size: 18px;
    padding: 40px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .image-error {
    color: #dc3545;
    font-size: 12px;
    font-style: italic;
    margin-top: 10px;
    padding: 10px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    nav {
        flex-direction: column;
        align-items: center;
    }
    
    nav button {
        width: 200px;
    }
    
    form {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    form label {
        justify-self: start;
        margin-bottom: 5px;
    }
    
    .survey-card {
        flex-direction: column;
        gap: 20px;
    }
    
    .survey-attachments {
        align-self: center;
        width: 100%;
    }
    
    .survey-image img {
        max-width: 100%;
        max-height: 200px;
    }
    
    .pagination-controls {
        flex-direction: column;
        gap: 15px;
    }
    }

    /* Enhanced focus states for accessibility */
    *:focus {
    outline: 3px solid rgba(88, 92, 44, 0.5);
    outline-offset: 2px;
    }

    /* Smooth scrolling */
    html {
    scroll-behavior: smooth;
    }

    /* Search container styling */
    .search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    }

    .search-container input {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    min-width: 250px;
    flex: 1;
    max-width: 400px;
    }

    .search-container input:focus {
    border-color: #585c2c;
    outline: none;
    }

    .search-container button {
    padding: 10px 20px;
    background: #585c2c;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    }

    .search-container button:hover {
    background: #6a6f35;
    }

    .search-container button:last-child {
    background: #666;
    }

    .search-container button:last-child:hover {
    background: #777;
    }
  </style>
</head>
<body>
  <h1>SkySenderos</h1>

  <nav>
    <button onclick="showPage('add-survey')">Add Survey</button>
    <button onclick="showPage('view-surveys')">View Surveys</button>
  </nav>

  <!-- Add Survey Page -->
  <div id="add-survey" class="page active">
    <h2>Add Drone Survey to Skybase</h2>
    <form id="survey-form">
      <label for="pilot">Pilot Name:</label>
      <input type="text" id="pilot" name="pilot" required />

      <label for="drone_name">Drone Name:</label>
      <select id="drone_name" name="drone_name" required>
        <option value="">-</option>
        <option value="SPUR">SPUR</option>
        <option value="VAQUERO">VAQUERO</option>
        <option value="SMOKEY">SMOKEY</option>
        <option value="TEX">TEX</option>
        <option value="COLT">COLT</option>
        <option value="BEVO">BEVO</option>
        <option value="PECOS">PECOS</option>
        <option value="RANGER">RANGER</option>
      </select>

      <label for="property_name">Property Name:</label>
      <input type="text" id="property_name" name="property_name" required />

      <label for="scheduled_time">Scheduled Date:</label>
      <input type="date" id="scheduled_time" name="scheduled_time" required />

      <label for="coordinates">Coordinates:</label>
      <input type="text" id="coordinates" name="coordinates" required />

      <label for="gate_code">Gate Code:</label>
      <input type="text" id="gate_code" name="gate_code" />

      <label for="google_sheets_id">Google Sheets ID:</label>
      <input type="text" id="google_sheets_id" name="google_sheets_id" />

      <label for="notes">Notes:</label>
      <textarea id="notes" name="notes"></textarea>

      <label for="driving_instructions">Driving Instructions:</label>
      <textarea id="driving_instructions" name="driving_instructions"></textarea>

      <label for="image">Survey Image:</label>
      <div class="file-input-group">
        <input type="file" id="image" name="image" accept="image/*" required />
        <span class="file-input-label">Required - Upload an image file</span>
      </div>

      <label for="pdf">Survey PDF:</label>
      <div class="file-input-group">
        <input type="file" id="pdf" name="pdf" accept=".pdf,application/pdf" />
        <span class="file-input-label">Optional - Upload a PDF document</span>
      </div>

      <button type="submit">Submit</button>
    </form>

  </div>

  <!-- Edit Survey Page -->
  <div id="edit-survey" class="page">
    <h2>Edit Drone Survey</h2>
    <form id="edit-survey-form">
      <label for="edit-pilot">Pilot Name:</label>
      <input type="text" id="edit-pilot" name="pilot" required />

      <label for="edit-drone_name">Drone Name:</label>
      <select id="edit-drone_name" name="drone_name" required>
        <option value="">-</option>
        <option value="SPUR">SPUR</option>
        <option value="VAQUERO">VAQUERO</option>
        <option value="SMOKEY">SMOKEY</option>
        <option value="TEX">TEX</option>
        <option value="COLT">COLT</option>
        <option value="BEVO">BEVO</option>
        <option value="PECOS">PECOS</option>
        <option value="RANGER">RANGER</option>
      </select>

      <label for="edit-property_name">Property Name:</label>
      <input type="text" id="edit-property_name" name="property_name" required />

      <label for="edit-scheduled_time">Scheduled Date:</label>
      <input type="date" id="edit-scheduled_time" name="scheduled_time" required />

      <label for="edit-coordinates">Coordinates:</label>
      <input type="text" id="edit-coordinates" name="coordinates" required />

      <label for="edit-gate_code">Gate Code:</label>
      <input type="text" id="edit-gate_code" name="gate_code" />

      <label for="edit-google_sheets_id">Google Sheets ID:</label>
      <input type="text" id="edit-google_sheets_id" name="google_sheets_id" />

      <label for="edit-notes">Notes:</label>
      <textarea id="edit-notes" name="notes"></textarea>

      <label for="edit-driving_instructions">Driving Instructions:</label>
      <textarea id="edit-driving_instructions" name="driving_instructions"></textarea>

      <label for="edit-image">Survey Image:</label>
      <div class="file-input-group">
        <input type="file" id="edit-image" name="image" accept="image/*" />
        <span class="file-input-label">Optional - Leave blank to keep current image</span>
      </div>

      <label for="edit-pdf">Survey PDF:</label>
      <div class="file-input-group">
        <input type="file" id="edit-pdf" name="pdf" accept=".pdf,application/pdf" />
        <span class="file-input-label">Optional - Leave blank to keep current PDF</span>
      </div>

      <button type="submit">Finish Editing</button>
      <button type="button" onclick="showPage('view-surveys')">Cancel</button>
    </form>

  </div>

  <!-- View Surveys Page -->
  <div id="view-surveys" class="page">
    <h2>All Drone Surveys</h2>

    <div class="search-container">
      <input type="text" id="property-search" placeholder="Search by property name..." />
      <button onclick="searchSurveys()">Search</button>
      <button onclick="clearSearch()">Clear</button>
    </div>
    <div id="search-summary" style="margin: 10px 0; font-style: italic; color: #666;"></div>

    <div class="pagination-controls">
      <button id="prev-btn" onclick="goToPreviousPage()" disabled>Previous</button>
      <span class="pagination-info">Page <span id="current-page">1</span></span>
      <button id="next-btn" onclick="goToNextPage()">Next</button>
    </div>

    <div id="loading" class="loading" style="display: none;">Loading surveys...</div>
    
    <div id="surveys-container" class="survey-card-list">
      <!-- Individual survey cards will be inserted here via JavaScript -->
    </div>

    <div id="no-surveys" class="no-surveys" style="display: none;">
      No surveys found.
    </div>
  </div>

  <script>
    const API_URL = 'https://zhtq9c7gge.execute-api.us-east-2.amazonaws.com/prod/surveys';
    const API_KEY = 'Sky193124141233';
    const PAGE_SIZE = 5;

    // Pagination state
    let currentPage = 1;
    let isLoading = false;
    let currentEditingSurvey = null;
    let currentSearch = '';
    let totalResults = 0;

    function showPage(pageId) {
      document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
      });
      document.getElementById(pageId).classList.add('active');

      if (pageId === 'view-surveys') {
        currentPage = 1;
        currentSearch = '';
        document.getElementById('property-search').value = '';
        updateSearchSummary();
        updatePaginationUI();
        fetchSurveys();
      } else if (pageId === 'add-survey') {
        // Clear file inputs when showing add survey page (unless coming from duplicate)
        // This prevents any residual file data from previous operations
        const imageInput = document.getElementById('image');
        const pdfInput = document.getElementById('pdf');

        // Only clear if not coming from a duplicate operation
        // (duplicate operation sets files intentionally)
        if (imageInput && !imageInput.dataset.fromDuplicate) {
          imageInput.value = '';
        }
        if (pdfInput && !pdfInput.dataset.fromDuplicate) {
          pdfInput.value = '';
        }

        // Clear the duplicate flags
        if (imageInput) imageInput.dataset.fromDuplicate = '';
        if (pdfInput) pdfInput.dataset.fromDuplicate = '';
      }
    }

    function searchSurveys() {
      const searchValue = document.getElementById('property-search').value.trim();
      console.log('Searching for:', searchValue);

      currentSearch = searchValue;
      currentPage = 1;
      totalResults = 0;

      updateSearchSummary();
      updatePaginationUI();
      fetchSurveys();
    }

    function clearSearch() {
      document.getElementById('property-search').value = '';
      currentSearch = '';
      currentPage = 1;
      totalResults = 0;

      updateSearchSummary();
      updatePaginationUI();
      fetchSurveys();
    }

    function updateSearchSummary() {
      const summaryElement = document.getElementById('search-summary');
      if (currentSearch) {
        summaryElement.textContent = `Searching for: "${currentSearch}"`;
        summaryElement.style.display = 'block';
      } else {
        summaryElement.textContent = '';
        summaryElement.style.display = 'none';
      }
    }

    // Helper function to convert date to local date string for input fields
    function dateToLocalDateString(dateString) {
      if (!dateString) return '';

      const date = new Date(dateString);

      // Get the local date components to avoid timezone issues
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    }

    // Helper function to convert local date input to proper datetime for API
    function localDateToDateTime(dateString) {
      if (!dateString) return null;

      // Create date at noon local time to avoid timezone edge cases
      const date = new Date(dateString + 'T12:00:00');
      return date.toISOString();
    }

    // Add enter key support for search
    document.addEventListener('DOMContentLoaded', function() {
      const searchInput = document.getElementById('property-search');
      if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            searchSurveys();
          }
        });
      }
    });

    // PDF Click Handler with debugging
    function handlePdfClick(pdfId) {
      console.log('PDF clicked:', pdfId);
      const pdfData = window[`pdfData_${pdfId}`];
      
      if (pdfData) {
        console.log('Opening PDF modal with:', pdfData.src, pdfData.title);
        openPdfModal(pdfData.src, pdfData.title);
      } else {
        console.error('PDF data not found for:', pdfId);
        alert('Error: PDF data not found');
      }
    }

    // PDF Modal Functions
    function openPdfModal(pdfSrc, title) {
      console.log('openPdfModal called with:', pdfSrc, title);
      
      // Create modal if it doesn't exist
      let modal = document.getElementById('pdf-modal');
      if (!modal) {
        console.log('Creating new PDF modal');
        modal = document.createElement('div');
        modal.id = 'pdf-modal';
        modal.className = 'pdf-modal';
        modal.innerHTML = `
          <div class="pdf-modal-content">
            <div class="pdf-modal-header">
              <h3 id="pdf-modal-title">PDF Viewer</h3>
              <button class="pdf-modal-close" onclick="closePdfModal()">&times;</button>
            </div>
            <iframe id="pdf-modal-frame" src="" title="PDF Viewer"></iframe>
          </div>
        `;
        document.body.appendChild(modal);
        
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
          if (e.target === modal) {
            closePdfModal();
          }
        });
        
        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
          if (e.key === 'Escape' && modal.style.display === 'block') {
            closePdfModal();
          }
        });
      }
      
      // Set PDF source and title
      console.log('Setting modal content');
      document.getElementById('pdf-modal-title').textContent = `${title} - PDF Viewer`;
      document.getElementById('pdf-modal-frame').src = pdfSrc + '#toolbar=1';
      modal.style.display = 'block';
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
      
      console.log('PDF modal should now be visible');
    }

    function closePdfModal() {
      console.log('Closing PDF modal');
      const modal = document.getElementById('pdf-modal');
      if (modal) {
        modal.style.display = 'none';
        document.getElementById('pdf-modal-frame').src = ''; // Stop loading
        document.body.style.overflow = 'auto'; // Restore scrolling
      }
    }

    function showPdfError(pdfId) {
      const viewer = document.getElementById(`${pdfId}-viewer`);
      if (viewer) {
        // Get the PDF data from global storage
        const pdfData = window[`pdfData_${pdfId}`];
        
        viewer.innerHTML = `
          <div class="pdf-error" onclick="handlePdfClick('${pdfId}')" title="Click to view PDF in fullscreen">
            <div class="pdf-icon" style="width: 40px; height: 40px; font-size: 16px; margin-bottom: 10px;">PDF</div>
            <div>PDF could not be displayed</div>
            <div style="font-size: 12px; margin-top: 5px;">Click to view in fullscreen</div>
          </div>
        `;
        viewer.style.cursor = 'pointer';
      }
    }

    function goToPreviousPage() {
      if (currentPage > 1 && !isLoading) {
        currentPage--;
        updatePaginationUI();
        fetchSurveys();
      }
    }

    function goToNextPage() {
      if (!isLoading) {
        currentPage++;
        updatePaginationUI();
        fetchSurveys();
      }
    }

    function updatePaginationUI() {
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');
      const currentPageSpan = document.getElementById('current-page');
      
      currentPageSpan.textContent = currentPage;
      prevBtn.disabled = currentPage === 1 || isLoading;
      nextBtn.disabled = isLoading;
    }

    const form = document.getElementById('survey-form');
    const editForm = document.getElementById('edit-survey-form');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(form);

      try {
        const imageFile = formData.get('image');
        const pdfFile = formData.get('pdf');

        console.log('Image file:', imageFile);
        console.log('PDF file:', pdfFile);
        
        if (!imageFile || !imageFile.type.startsWith('image/')) {
          throw new Error("Please upload a valid image file.");
        }
        
        const base64Image = await toBase64(imageFile);
        let base64Pdf = null;
        
        // Handle PDF upload if provided
        if (pdfFile && pdfFile.size > 0) {
          if (pdfFile.type !== 'application/pdf') {
            throw new Error("Please upload a valid PDF file.");
          }
          base64Pdf = await toBase64(pdfFile);
        }

        const dateOnly = formData.get('scheduled_time');
        const isoUtc = localDateToDateTime(dateOnly);

        const data = {
            pilot: formData.get('pilot'),
            drone_name: formData.get('drone_name'),
            scheduled_time: isoUtc,
            coordinates: formData.get('coordinates'),
            gate_code: formData.get('gate_code'),
            notes: formData.get('notes'),
            driving_instructions: formData.get('driving_instructions'),
            image_base64: base64Image,
            google_sheets_id: formData.get('google_sheets_id'),
            property_name: formData.get('property_name')
        };

        // Only add PDF if provided
        if (base64Pdf) {
          data.pdf_base64 = base64Pdf;
        }

        console.log('Submitting data:', data);

        const res = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': API_KEY
          },
          body: JSON.stringify(data)
        });

        const json = await res.json();
        console.log("Response: ", json)
        
        if (!res.ok) {
          throw new Error(`Failed to add survey: ${json.message || 'Unknown error'}`);
        }
        
        form.reset();
        alert('Survey added successfully!');
      } catch (err) {
        console.log("Error: ", err.message)
        alert(`Error: ${err.message}`);
      }
    });

    // Edit form submission handler
    editForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const formData = new FormData(editForm);

      try {
        if (!currentEditingSurvey || !currentEditingSurvey.id) {
          throw new Error("No survey selected for editing.");
        }

        const dateOnly = formData.get('scheduled_time');
        const isoUtc = localDateToDateTime(dateOnly);

        const data = {
            id: currentEditingSurvey.id,
            pilot: formData.get('pilot'),
            drone_name: formData.get('drone_name'),
            scheduled_time: isoUtc,
            coordinates: formData.get('coordinates'),
            gate_code: formData.get('gate_code'),
            notes: formData.get('notes'),
            driving_instructions: formData.get('driving_instructions'),
            google_sheets_id: formData.get('google_sheets_id'),
            property_name: formData.get('property_name')
        };

        // Only include image if a new one was uploaded
        const imageFile = formData.get('image');
        if (imageFile && imageFile.size > 0) {
          if (!imageFile.type.startsWith('image/')) {
            throw new Error("Please upload a valid image file.");
          }
          const base64Image = await toBase64(imageFile);
          data.image_base64 = base64Image;
        }

        // Only include PDF if a new one was uploaded
        const pdfFile = formData.get('pdf');
        if (pdfFile && pdfFile.size > 0) {
          if (pdfFile.type !== 'application/pdf') {
            throw new Error("Please upload a valid PDF file.");
          }
          const base64Pdf = await toBase64(pdfFile);
          data.pdf_base64 = base64Pdf;
        }

        const res = await fetch(API_URL, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': API_KEY
          },
          body: JSON.stringify(data)
        });

        const json = await res.json();
        console.log("Edit Response: ", json)
        
        if (!res.ok) {
          throw new Error(`Failed to update survey: ${json.message || 'Unknown error'}`);
        }

        alert('Survey updated successfully!');
        currentEditingSurvey = null;
        showPage('view-surveys');
      } catch (err) {
        console.log("Edit Error: ", err.message)
        alert(`Error: ${err.message}`);
      }
    });

    // Function to populate edit form with survey data
    function editSurvey(survey) {
      console.log("Editing survey:", survey);
      currentEditingSurvey = survey;
      
      // Populate form fields
      document.getElementById('edit-pilot').value = survey.pilot || '';
      document.getElementById('edit-drone_name').value = survey.drone_name || '';
      document.getElementById('edit-property_name').value = survey.property_name || '';
      document.getElementById('edit-coordinates').value = survey.coordinates || '';
      document.getElementById('edit-gate_code').value = survey.gate_code || '';
      document.getElementById('edit-google_sheets_id').value = survey.google_sheets_id || '';
      document.getElementById('edit-notes').value = survey.notes || '';
      document.getElementById('edit-driving_instructions').value = survey.driving_instructions || '';
      
      // Convert scheduled_time to date format for input (preserving local date)
      if (survey.scheduled_time) {
        const dateString = dateToLocalDateString(survey.scheduled_time);
        document.getElementById('edit-scheduled_time').value = dateString;
      }

      // Clear file inputs to prevent auto-upload of previous files
      const editImageInput = document.getElementById('edit-image');
      const editPdfInput = document.getElementById('edit-pdf');

      if (editImageInput) {
        editImageInput.value = '';
      }
      if (editPdfInput) {
        editPdfInput.value = '';
      }

      // Show edit page
      showPage('edit-survey');
    }

    // Function to duplicate survey data into add form
    async function duplicateSurvey(survey) {
      console.log("Duplicating survey:", survey);
      
      // Populate add form fields with survey data
      document.getElementById('pilot').value = survey.pilot || '';
      document.getElementById('drone_name').value = survey.drone_name || '';
      document.getElementById('property_name').value = survey.property_name || '';
      document.getElementById('coordinates').value = survey.coordinates || '';
      document.getElementById('gate_code').value = survey.gate_code || '';
      document.getElementById('google_sheets_id').value = survey.google_sheets_id || '';
      document.getElementById('notes').value = survey.notes || '';
      document.getElementById('driving_instructions').value = survey.driving_instructions || '';
      
      // Convert scheduled_time to date format for input (preserving local date)
      if (survey.scheduled_time) {
        const dateString = dateToLocalDateString(survey.scheduled_time);
        document.getElementById('scheduled_time').value = dateString;
      }
      
      let imageSuccess = false;
      let pdfSuccess = false;
      let errors = [];
      
      // Copy image file if it exists
      if (survey.image) {
        try {
          console.log("Processing image for duplication...");
          const imageDataUrl = processImageData(survey.image);
          console.log("Image data URL created:", imageDataUrl ? "Success" : "Failed");
          
          if (imageDataUrl) {
            const imageFile = await dataURLToFile(imageDataUrl, `duplicated-image-${survey.property_name || 'survey'}.jpg`);
            console.log("Image file created:", imageFile.name, imageFile.size, "bytes");
            
            const imageInput = document.getElementById('image');

            // Create a new FileList with our file
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(imageFile);
            imageInput.files = dataTransfer.files;

            // Mark as coming from duplicate to prevent clearing
            imageInput.dataset.fromDuplicate = 'true';

            console.log("Image file assigned to input");
            imageSuccess = true;
          } else {
            errors.push("Could not process image data");
          }
        } catch (error) {
          console.error("Error duplicating image:", error);
          errors.push(`Image duplication failed: ${error.message}`);
        }
      }
      
      // Copy PDF file if it exists
      if (survey.pdf) {
        try {
          console.log("Processing PDF for duplication...");
          const pdfDataUrl = processPdfData(survey.pdf);
          console.log("PDF data URL created:", pdfDataUrl ? "Success" : "Failed");
          
          if (pdfDataUrl) {
            const pdfFile = await dataURLToFile(pdfDataUrl, `duplicated-pdf-${survey.property_name || 'survey'}.pdf`);
            console.log("PDF file created:", pdfFile.name, pdfFile.size, "bytes");
            
            const pdfInput = document.getElementById('pdf');

            // Create a new FileList with our file
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(pdfFile);
            pdfInput.files = dataTransfer.files;

            // Mark as coming from duplicate to prevent clearing
            pdfInput.dataset.fromDuplicate = 'true';

            console.log("PDF file assigned to input");
            pdfSuccess = true;
          } else {
            errors.push("Could not process PDF data");
          }
        } catch (error) {
          console.error("Error duplicating PDF:", error);
          errors.push(`PDF duplication failed: ${error.message}`);
        }
      }
      
      // Show add page
      showPage('add-survey');
      
      // Show appropriate success/error message
      let message = "Survey text data duplicated successfully!";
      
      if (imageSuccess && pdfSuccess) {
        message += " Both image and PDF files have been copied to the form.";
      } else if (imageSuccess && !pdfSuccess) {
        message += " Image file has been copied to the form.";
        if (survey.pdf) {
          message += " Note: PDF file could not be duplicated - you'll need to upload it manually.";
        }
      } else if (!imageSuccess && pdfSuccess) {
        message += " PDF file has been copied to the form.";
        if (survey.image) {
          message += " Note: Image file could not be duplicated - you'll need to upload it manually.";
        }
      } else if (errors.length > 0) {
        message += " Note: Files could not be duplicated - you'll need to upload them manually.";
        console.log("Duplication errors:", errors);
      }
      
      alert(message);
    }

    function toBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result.split(',')[1]; // only base64 part
          resolve(base64String);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    }

    async function fetchSurveys() {
      if (isLoading) return;
      
      console.log("fetching surveys for page", currentPage);
      setLoadingState(true);

      try {
        const pilot = 'Master';
        const droneName = 'Master';
        const datetime = new Date().toISOString();

        const url = new URL(API_URL);
        url.searchParams.append('pilot', pilot);
        url.searchParams.append('drone_name', droneName);
        url.searchParams.append('datetime', datetime);
        url.searchParams.append('page', currentPage.toString());

        if (currentSearch) {
          url.searchParams.append('property_name', currentSearch);
        }

        console.log("Fetching URL:", url.toString());

        const res = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'x-api-key': API_KEY
          }
        });

        console.log("Response status:", res.status);
        console.log("Response ok:", res.ok);

        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        const surveys = await res.json();
        console.log("got response", surveys);
        
        const surveyData = surveys.data || [];
        renderSurveys(surveyData);
        
        // Update pagination controls based on results
        updatePaginationAfterFetch(surveyData);
        
      } catch (err) {
        console.error("error fetching", err);
        console.error("Error details:", err.message);
        
        // More specific error messages
        if (err.message.includes('Failed to fetch')) {
          showError('Network error: Unable to connect to the server. Please check your connection and try again.');
        } else if (err.message.includes('HTTP error')) {
          showError(`Server error: ${err.message}`);
        } else {
          showError('Error loading surveys. Please try again.');
        }
      } finally {
        setLoadingState(false);
      }
    }

    function setLoadingState(loading) {
      isLoading = loading;
      const loadingDiv = document.getElementById('loading');
      const container = document.getElementById('surveys-container');
      const noSurveys = document.getElementById('no-surveys');
      
      if (loading) {
        loadingDiv.style.display = 'block';
        container.style.display = 'none';
        noSurveys.style.display = 'none';
      } else {
        loadingDiv.style.display = 'none';
        container.style.display = 'flex';
      }
      
      updatePaginationUI();
    }

    function updatePaginationAfterFetch(surveyData) {
      const nextBtn = document.getElementById('next-btn');
      
      // If we got less than PAGE_SIZE results, disable next button
      if (surveyData.length < PAGE_SIZE) {
        nextBtn.disabled = true;
      } else {
        nextBtn.disabled = false;
      }
      
      // If we're on page 1 and got no results, show no surveys message
      if (currentPage === 1 && surveyData.length === 0) {
        document.getElementById('no-surveys').style.display = 'block';
        document.getElementById('surveys-container').style.display = 'none';
      } else {
        document.getElementById('no-surveys').style.display = 'none';
      }
    }

    function showError(message) {
      const container = document.getElementById('surveys-container');
      container.style.display = 'flex';
      container.innerHTML = `<div style="text-align: center; color: #d32f2f; margin: 20px 0;">${message}</div>`;
      document.getElementById('no-surveys').style.display = 'none';
    }

    // Function to process PDF data and create download link
    function processPdfData(pdfData) {
      console.log("Processing PDF data:", typeof pdfData, pdfData);
      
      // If it's null or undefined, return null
      if (!pdfData) {
        console.log("No PDF data provided");
        return null;
      }
      
      // If it's already a data URL string, return as is
      if (typeof pdfData === 'string' && pdfData.startsWith('data:')) {
        console.log("PDF is already a data URL");
        return pdfData;
      }
      
      // If it's a base64 string without data URL prefix
      if (typeof pdfData === 'string' && !pdfData.startsWith('data:')) {
        console.log("PDF is base64 string, adding data URL prefix");
        return `data:application/pdf;base64,${pdfData}`;
      }
      
      // Handle Buffer objects with data property (Node.js style)
      if (pdfData && typeof pdfData === 'object' && pdfData.type === 'Buffer' && Array.isArray(pdfData.data)) {
        console.log("Processing PDF Buffer object with data array");
        try {
          const uint8Array = new Uint8Array(pdfData.data);
          const base64 = uint8ArrayToBase64(uint8Array);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF Buffer object:", error);
          return null;
        }
      }
      
      // Handle ArrayBuffer
      if (pdfData instanceof ArrayBuffer) {
        console.log("Processing PDF ArrayBuffer");
        try {
          const uint8Array = new Uint8Array(pdfData);
          const base64 = uint8ArrayToBase64(uint8Array);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF ArrayBuffer:", error);
          return null;
        }
      }
      
      // Handle Uint8Array
      if (pdfData instanceof Uint8Array) {
        console.log("Processing PDF Uint8Array");
        try {
          const base64 = uint8ArrayToBase64(pdfData);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF Uint8Array:", error);
          return null;
        }
      }
      
      // Handle regular arrays (assume they're byte arrays)
      if (Array.isArray(pdfData)) {
        console.log("Processing PDF array as byte array");
        try {
          const uint8Array = new Uint8Array(pdfData);
          const base64 = uint8ArrayToBase64(uint8Array);
          return `data:application/pdf;base64,${base64}`;
        } catch (error) {
          console.error("Error processing PDF array:", error);
          return null;
        }
      }
      
      console.warn("Unknown PDF data format:", typeof pdfData);
      return null;
    }

    // Improved function to handle different image data formats
    function processImageData(imageData) {
      console.log("Processing image data:", typeof imageData, imageData);
      
      // If it's null or undefined, return null
      if (!imageData) {
        console.log("No image data provided");
        return null;
      }
      
      // If it's already a data URL string, return as is
      if (typeof imageData === 'string' && imageData.startsWith('data:')) {
        console.log("Image is already a data URL");
        return imageData;
      }
      
      // If it's a base64 string without data URL prefix
      if (typeof imageData === 'string' && !imageData.startsWith('data:')) {
        console.log("Image is base64 string, adding data URL prefix");
        // Try to detect if it's PNG or JPEG based on first few characters
        const isPNG = imageData.startsWith('iVBORw0KGgo'); // PNG signature in base64
        const mimeType = isPNG ? 'image/png' : 'image/jpeg';
        return `data:${mimeType};base64,${imageData}`;
      }
      
      // Handle Buffer objects with data property (Node.js style)
      if (imageData && typeof imageData === 'object' && imageData.type === 'Buffer' && Array.isArray(imageData.data)) {
        console.log("Processing Buffer object with data array");
        try {
          const uint8Array = new Uint8Array(imageData.data);
          const base64 = uint8ArrayToBase64(uint8Array);
          
          // Detect image type from first few bytes
          const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
          const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
          
          let mimeType = 'image/png'; // default to PNG
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          console.log("Detected image type:", mimeType);
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing Buffer object:", error);
          return null;
        }
      }
      
      // Handle ArrayBuffer
      if (imageData instanceof ArrayBuffer) {
        console.log("Processing ArrayBuffer");
        try {
          const uint8Array = new Uint8Array(imageData);
          const base64 = uint8ArrayToBase64(uint8Array);
          
          // Detect image type from first few bytes
          const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
          const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
          
          let mimeType = 'image/png'; // default to PNG
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing ArrayBuffer:", error);
          return null;
        }
      }
      
      // Handle Uint8Array
      if (imageData instanceof Uint8Array) {
        console.log("Processing Uint8Array");
        try {
          const base64 = uint8ArrayToBase64(imageData);
          
          // Detect image type from first few bytes
          const isPNG = imageData[0] === 0x89 && imageData[1] === 0x50 && imageData[2] === 0x4E && imageData[3] === 0x47;
          const isJPEG = imageData[0] === 0xFF && imageData[1] === 0xD8;
          
          let mimeType = 'image/png'; // default to PNG
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing Uint8Array:", error);
          return null;
        }
      }
      
      // Handle regular arrays (assume they're byte arrays)
      if (Array.isArray(imageData)) {
        console.log("Processing array as byte array");
        try {
          const uint8Array = new Uint8Array(imageData);
          const base64 = uint8ArrayToBase64(uint8Array);
          
          // Detect image type from first few bytes
          const isPNG = uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4E && uint8Array[3] === 0x47;
          const isJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8;
          
          let mimeType = 'image/png'; // default to PNG
          if (isJPEG) {
            mimeType = 'image/jpeg';
          } else if (isPNG) {
            mimeType = 'image/png';
          }
          
          return `data:${mimeType};base64,${base64}`;
        } catch (error) {
          console.error("Error processing array:", error);
          return null;
        }
      }
      
      console.warn("Unknown image data format:", typeof imageData);
      return null;
    }

    // Efficient Uint8Array to base64 conversion
    function uint8ArrayToBase64(uint8Array) {
      const CHUNK_SIZE = 0x8000; // 32KB chunks to avoid call stack overflow
      let result = '';
      
      for (let i = 0; i < uint8Array.length; i += CHUNK_SIZE) {
        const chunk = uint8Array.subarray(i, i + CHUNK_SIZE);
        result += String.fromCharCode.apply(null, chunk);
      }
      
      return btoa(result);
    }

    // Store survey data for easy access
    let surveyDataStore = {};

    async function renderSurveys(data) {
      console.log("rendering surveys");
      const container = document.getElementById('surveys-container');
      const noSurveys = document.getElementById('no-surveys');
      
      container.innerHTML = '';
      surveyDataStore = {}; // Clear previous data

      if (data.length === 0) {
        if (currentPage === 1) {
          noSurveys.style.display = 'block';
          container.style.display = 'none';
        }
        return;
      }

      // Show container and hide no surveys message
      container.style.display = 'flex';
      noSurveys.style.display = 'none';

      for (let i = 0; i < data.length; i++) {
        const survey = data[i];
        console.log("rendering: ", survey);

        const card = document.createElement('div');
        card.className = 'survey-card';

        // Generate unique ID for this survey
        const surveyId = `survey_${currentPage}_${i}`;
        surveyDataStore[surveyId] = survey;

        // Process image data with improved handling
        let imageHtml = '';
        if (survey.image) {
          console.log("Survey has image data:", typeof survey.image);
          
          const imageSrc = processImageData(survey.image);
          
          if (imageSrc) {
            imageHtml = `
              <div class="survey-image">
                <img src="${imageSrc}" alt="Survey Image" 
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
                <div class="image-error" style="display: none;">Image failed to load</div>
              </div>
            `;
          } else {
            imageHtml = `
              <div class="survey-image">
                <div class="image-error">Unable to process image data</div>
              </div>
            `;
          }
        }

        // Process PDF data and create embedded viewer
        let pdfHtml = '';
        if (survey.pdf) {
          console.log("Survey has PDF data:", typeof survey.pdf);
          
          const pdfSrc = processPdfData(survey.pdf);
          
          if (pdfSrc) {
            const pdfId = `pdf-${surveyId}`;
            pdfHtml = `
              <div class="pdf-section">
                <div class="pdf-viewer" id="${pdfId}-viewer">
                  <iframe src="${pdfSrc}#toolbar=0&navpanes=0&scrollbar=0" 
                          title="Survey PDF" 
                          onerror="showPdfError('${pdfId}')">
                  </iframe>
                </div>
                <div class="pdf-controls">
                  <a href="javascript:void(0)" class="pdf-link fullscreen" onclick="openPdfModal('${pdfSrc}', '${survey.property_name || 'Survey'}')">
                    <div class="pdf-icon">⛶</div>
                    View Full
                  </a>
                  <a href="${pdfSrc}" class="pdf-link" download="survey-${survey.property_name || 'document'}.pdf">
                    <div class="pdf-icon">PDF</div>
                    Download
                  </a>
                </div>
              </div>
            `;
          } else {
            pdfHtml = `
              <div class="pdf-section">
                <div class="pdf-error">
                  <div class="pdf-icon" style="width: 40px; height: 40px; font-size: 16px; margin-bottom: 10px;">PDF</div>
                  <div>Unable to process PDF data</div>
                </div>
              </div>
            `;
          }
        }

        // Combine image and PDF in attachments section
        let attachmentsHtml = '';
        if (imageHtml || pdfHtml) {
          attachmentsHtml = `
            <div class="survey-attachments">
              ${imageHtml}
              ${pdfHtml}
            </div>
          `;
        }

        card.innerHTML = `
            <div class="survey-info">
                <p><strong>Property Name:</strong> ${survey.property_name || ''}</p>
                <p><strong>Pilot:</strong> ${survey.pilot || ''}</p>
                <p><strong>Drone:</strong> ${survey.drone_name || ''}</p>
                <p><strong>Scheduled Time:</strong> ${new Date(survey.scheduled_time).toLocaleString()}</p>
                <p><strong>Coordinates:</strong> ${survey.coordinates || ''}</p>
                <p><strong>Gate Code:</strong> ${survey.gate_code || ''}</p>
                <p><strong>Notes:</strong> ${survey.notes || ''}</p>
                <p><strong>Driving Instructions:</strong> ${survey.driving_instructions || ''}</p>
                <p><strong>Google Sheets ID:</strong> ${survey.google_sheets_id || ''}</p>
                <button class="edit-btn" onclick="editSurveyById('${surveyId}')">Edit</button>
                <button class="duplicate-btn" onclick="duplicateSurveyById('${surveyId}')">Duplicate</button>
                <button onclick="deleteSurveyById('${surveyId}')">Delete</button>
                <p><strong>ID:</strong> ${survey.id || ''}</p>
          </div>
          ${attachmentsHtml}
        `;

        container.appendChild(card);
      }
    }

    // helper function to delete the survey by id
    async function deleteSurveyById(surveyId) {
      const survey = surveyDataStore[surveyId];
      if (!survey) {
        alert('Error: Survey not found');
        return;
      }

      const confirmDelete = confirm(`Are you sure you want to delete the survey "${survey.property_name}" (ID: ${survey.id})?`);
      if (!confirmDelete) return;

      try {
        const url = new URL(API_URL);
        url.searchParams.append('id', survey.id.toString());

        const res = await fetch(url.toString(), {
          method: 'DELETE',
          headers: {
            'x-api-key': API_KEY
          }
        });

        if (!res.ok) {
          throw new Error(`Failed to delete survey (status ${res.status})`);
        }

        alert('Survey deleted and archived successfully');
        
        const cardElement = document.querySelector(`[onclick*="${surveyId}"]`)?.closest('.survey-card');
        if (cardElement) {
          cardElement.remove();
        }
        
        delete surveyDataStore[surveyId];

      } catch (err) {
        alert(`Error deleting survey: ${err.message}`);
      }
    }

    // Helper functions to get survey data by ID
    async function editSurveyById(surveyId) {
      const survey = surveyDataStore[surveyId];
      if (survey) {
        editSurvey(survey);
      } else {
        console.error('Survey not found:', surveyId);
      }
    }

    async function duplicateSurveyById(surveyId) {
      const survey = surveyDataStore[surveyId];
      if (survey) {
        await duplicateSurvey(survey);
      } else {
        console.error('Survey not found:', surveyId);
      }
    }

    // Utility function to convert data URL to File object
    async function dataURLToFile(dataURL, filename) {
      const response = await fetch(dataURL);
      const blob = await response.blob();
      return new File([blob], filename, { type: blob.type });
    }
  </script>
</body>
</html>